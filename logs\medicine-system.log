2025-07-31 18:00:18.945  INFO 1224 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 1224 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-07-31 18:00:18.953 DEBUG 1224 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-07-31 18:00:18.954  INFO 1224 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-07-31 18:00:19.069  INFO 1224 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-31 18:00:19.070  INFO 1224 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-31 18:00:20.957  INFO 1224 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 18:00:21.066  INFO 1224 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 98 ms. Found 10 JPA repository interfaces.
2025-07-31 18:00:21.996  INFO 1224 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-07-31 18:00:22.012  INFO 1224 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-31 18:00:22.013  INFO 1224 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-07-31 18:00:22.130  INFO 1224 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-31 18:00:22.131  INFO 1224 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3061 ms
2025-07-31 18:00:22.485  INFO 1224 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 18:00:22.570  INFO 1224 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-31 18:00:22.849  INFO 1224 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 18:00:23.035  INFO 1224 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-31 18:00:23.756  INFO 1224 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-31 18:00:23.779  INFO 1224 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-31 18:00:24.746  INFO 1224 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 18:00:24.758  INFO 1224 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 18:00:25.420  WARN 1224 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-31 18:00:25.919  WARN 1224 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: e6eb7952-dbe7-44cf-beeb-7b3fc3bdcf3f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 18:00:26.079  INFO 1224 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4eac21e3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@53a01505, org.springframework.security.web.context.SecurityContextPersistenceFilter@57052ffd, org.springframework.security.web.header.HeaderWriterFilter@55159158, org.springframework.security.web.csrf.CsrfFilter@77af1bed, org.springframework.security.web.authentication.logout.LogoutFilter@3517d5b1, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@122cc7d9, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@1d290d72, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@2a7de014, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@13b1d41f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@18a70d60, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6dc9d3d2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c1a42e2, org.springframework.security.web.session.SessionManagementFilter@176c5cc3, org.springframework.security.web.access.ExceptionTranslationFilter@2fe1fe78, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@39b4d37a]
2025-07-31 18:00:26.139  INFO 1224 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-31 18:00:26.208  INFO 1224 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 18:00:26.221  INFO 1224 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 7.798 seconds (JVM running for 14.194)
2025-07-31 18:18:19.171  INFO 1224 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 18:18:19.171  INFO 1224 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-31 18:18:19.173  INFO 1224 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-31 18:35:47.653  INFO 1224 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 18:35:47.666  INFO 1224 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-31 18:35:47.683  INFO 1224 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-31 18:40:26.016  INFO 11652 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 11652 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-07-31 18:40:26.018 DEBUG 11652 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-07-31 18:40:26.018  INFO 11652 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-07-31 18:40:26.099  INFO 11652 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-31 18:40:26.100  INFO 11652 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-31 18:40:27.058  INFO 11652 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 18:40:27.174  INFO 11652 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 105 ms. Found 10 JPA repository interfaces.
2025-07-31 18:40:27.846  INFO 11652 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-07-31 18:40:27.856  INFO 11652 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-31 18:40:27.856  INFO 11652 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-07-31 18:40:27.926  INFO 11652 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-31 18:40:27.926  INFO 11652 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1825 ms
2025-07-31 18:40:28.120  INFO 11652 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 18:40:28.174  INFO 11652 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-31 18:40:28.341  INFO 11652 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 18:40:28.441  INFO 11652 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-31 18:40:28.838  INFO 11652 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-31 18:40:28.856  INFO 11652 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-31 18:40:29.552  INFO 11652 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 18:40:29.561  INFO 11652 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 18:40:29.628  WARN 11652 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-31 18:40:30.186  WARN 11652 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: def785f1-13b4-46ab-8fc7-8677f05f0d4b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 18:40:30.287  INFO 11652 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2e371408, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5543085b, org.springframework.security.web.context.SecurityContextPersistenceFilter@16243f4, org.springframework.security.web.header.HeaderWriterFilter@21ae9879, org.springframework.web.filter.CorsFilter@23862fc1, org.springframework.security.web.authentication.logout.LogoutFilter@2974e0aa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@34a4134b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6e758417, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@29829114, org.springframework.security.web.session.SessionManagementFilter@31e10189, org.springframework.security.web.access.ExceptionTranslationFilter@3c1926ab, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1d89d5e3]
2025-07-31 18:40:30.654  INFO 11652 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-31 18:40:30.692  INFO 11652 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 18:40:30.702  INFO 11652 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.259 seconds (JVM running for 6.371)
2025-07-31 18:40:43.939  INFO 11652 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 18:40:43.939  INFO 11652 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-31 18:40:43.941  INFO 11652 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-31 18:50:11.255  INFO 11652 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 18:50:11.260  INFO 11652 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-31 18:50:11.272  INFO 11652 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-31 18:50:19.132  INFO 6808 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 6808 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-07-31 18:50:19.133 DEBUG 6808 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-07-31 18:50:19.134  INFO 6808 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-07-31 18:50:19.218  INFO 6808 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-31 18:50:19.218  INFO 6808 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-31 18:50:20.145  INFO 6808 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 18:50:20.267  INFO 6808 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 112 ms. Found 10 JPA repository interfaces.
2025-07-31 18:50:21.021  INFO 6808 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-07-31 18:50:21.034  INFO 6808 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-31 18:50:21.034  INFO 6808 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-07-31 18:50:21.121  INFO 6808 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-31 18:50:21.121  INFO 6808 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1902 ms
2025-07-31 18:50:21.317  INFO 6808 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 18:50:21.369  INFO 6808 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-31 18:50:21.565  INFO 6808 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 18:50:21.667  INFO 6808 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-31 18:50:22.049  INFO 6808 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-31 18:50:22.066  INFO 6808 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-31 18:50:22.770  INFO 6808 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 18:50:22.782  INFO 6808 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 18:50:22.846  WARN 6808 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-31 18:50:23.367  WARN 6808 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 3de74fdd-5f39-4998-ac9a-0f3ba72e009d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 18:50:23.464  INFO 6808 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@18783ef9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@d8d34c9, org.springframework.security.web.context.SecurityContextPersistenceFilter@71cdeabf, org.springframework.security.web.header.HeaderWriterFilter@2cab44e6, org.springframework.web.filter.CorsFilter@5cd1d95b, org.springframework.security.web.authentication.logout.LogoutFilter@4dda855e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1ff29d6c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3a569871, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7b13c432, org.springframework.security.web.session.SessionManagementFilter@6b5c55da, org.springframework.security.web.access.ExceptionTranslationFilter@4579c3b5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6ca81810]
2025-07-31 18:50:23.834  INFO 6808 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-31 18:50:23.872  INFO 6808 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 18:50:23.881  INFO 6808 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.305 seconds (JVM running for 6.232)
2025-07-31 18:50:30.725  INFO 6808 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 18:50:30.725  INFO 6808 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-31 18:50:30.726  INFO 6808 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-07-31 18:54:41.682  INFO 6808 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 18:54:41.686  INFO 6808 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-31 18:54:41.695  INFO 6808 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-31 18:54:45.834  INFO 21636 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 21636 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-07-31 18:54:45.835 DEBUG 21636 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-07-31 18:54:45.836  INFO 21636 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-07-31 18:54:45.918  INFO 21636 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-31 18:54:45.919  INFO 21636 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-31 18:54:46.710  INFO 21636 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 18:54:46.804  INFO 21636 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 85 ms. Found 10 JPA repository interfaces.
2025-07-31 18:54:47.393  INFO 21636 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-07-31 18:54:47.402  INFO 21636 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-31 18:54:47.403  INFO 21636 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-07-31 18:54:47.474  INFO 21636 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-31 18:54:47.474  INFO 21636 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1555 ms
2025-07-31 18:54:47.696  INFO 21636 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 18:54:47.747  INFO 21636 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-31 18:54:47.932  INFO 21636 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 18:54:48.038  INFO 21636 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-31 18:54:48.425  INFO 21636 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-31 18:54:48.441  INFO 21636 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-31 18:54:49.126  INFO 21636 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 18:54:49.136  INFO 21636 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 18:54:49.201  WARN 21636 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-31 18:54:49.739  WARN 21636 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 633aa1e5-7d22-47ed-9230-8c7874b1b328

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 18:54:49.843  INFO 21636 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4cfd6310, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@18783ef9, org.springframework.security.web.context.SecurityContextPersistenceFilter@64caacbc, org.springframework.security.web.header.HeaderWriterFilter@1a93ed59, org.springframework.web.filter.CorsFilter@d8d34c9, org.springframework.security.web.authentication.logout.LogoutFilter@25d30df7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@783683d9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@779c7c8d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5cd1d95b, org.springframework.security.web.session.SessionManagementFilter@13d0c842, org.springframework.security.web.access.ExceptionTranslationFilter@5e3c4bd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@27143f6a]
2025-07-31 18:54:50.250  INFO 21636 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-31 18:54:50.290  INFO 21636 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 18:54:50.301  INFO 21636 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.014 seconds (JVM running for 5.967)
2025-07-31 18:54:54.133  INFO 21636 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 18:54:54.133  INFO 21636 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-31 18:54:54.134  INFO 21636 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-31 18:57:29.178  WARN 21636 --- [http-nio-8080-exec-3] o.s.s.c.bcrypt.BCryptPasswordEncoder     : Encoded password does not look like BCrypt
2025-07-31 18:57:37.117  WARN 21636 --- [http-nio-8080-exec-4] o.s.s.c.bcrypt.BCryptPasswordEncoder     : Encoded password does not look like BCrypt
2025-07-31 18:57:41.338  INFO 21636 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 18:57:41.343  INFO 21636 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-31 18:57:41.350  INFO 21636 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-31 18:57:47.914  INFO 19776 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 19776 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-07-31 18:57:47.916 DEBUG 19776 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-07-31 18:57:47.917  INFO 19776 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-07-31 18:57:48.004  INFO 19776 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-31 18:57:48.004  INFO 19776 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-31 18:57:48.899  INFO 19776 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 18:57:48.992  INFO 19776 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 83 ms. Found 10 JPA repository interfaces.
2025-07-31 18:57:49.589  INFO 19776 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-07-31 18:57:49.599  INFO 19776 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-31 18:57:49.599  INFO 19776 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-07-31 18:57:49.673  INFO 19776 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-31 18:57:49.674  INFO 19776 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1669 ms
2025-07-31 18:57:49.856  INFO 19776 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 18:57:49.902  INFO 19776 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-31 18:57:50.067  INFO 19776 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 18:57:50.167  INFO 19776 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-31 18:57:50.575  INFO 19776 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-31 18:57:50.592  INFO 19776 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-31 18:57:51.319  INFO 19776 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 18:57:51.329  INFO 19776 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 18:57:51.397  WARN 19776 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-31 18:57:51.950  WARN 19776 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: cf769fea-8bad-4430-846f-10e14f4f618d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 18:57:52.054  INFO 19776 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4cfd6310, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@18783ef9, org.springframework.security.web.context.SecurityContextPersistenceFilter@64caacbc, org.springframework.security.web.header.HeaderWriterFilter@1a93ed59, org.springframework.web.filter.CorsFilter@d8d34c9, org.springframework.security.web.authentication.logout.LogoutFilter@25d30df7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@783683d9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@779c7c8d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5cd1d95b, org.springframework.security.web.session.SessionManagementFilter@13d0c842, org.springframework.security.web.access.ExceptionTranslationFilter@5e3c4bd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@27143f6a]
2025-07-31 18:57:52.413  INFO 19776 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-31 18:57:52.450  INFO 19776 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 18:57:52.460  INFO 19776 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.118 seconds (JVM running for 6.059)
2025-07-31 18:57:56.219  INFO 19776 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 18:57:56.220  INFO 19776 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-31 18:57:56.223  INFO 19776 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-31 19:10:49.895  INFO 19776 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 19:10:49.900  INFO 19776 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-31 19:10:49.914  INFO 19776 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-31 19:10:54.505  INFO 13064 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 13064 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-07-31 19:10:54.507 DEBUG 13064 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-07-31 19:10:54.508  INFO 13064 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-07-31 19:10:54.602  INFO 13064 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-31 19:10:54.602  INFO 13064 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-31 19:10:55.401  INFO 13064 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 19:10:55.494  INFO 13064 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 82 ms. Found 10 JPA repository interfaces.
2025-07-31 19:10:56.156  INFO 13064 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-07-31 19:10:56.168  INFO 13064 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-31 19:10:56.169  INFO 13064 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-07-31 19:10:56.248  INFO 13064 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-31 19:10:56.248  INFO 13064 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1645 ms
2025-07-31 19:10:56.499  INFO 13064 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 19:10:56.573  INFO 13064 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-31 19:10:56.817  INFO 13064 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 19:10:56.997  INFO 13064 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-31 19:10:57.522  INFO 13064 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-31 19:10:57.542  INFO 13064 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-31 19:10:58.297  INFO 13064 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 19:10:58.308  INFO 13064 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 19:10:58.361  WARN 13064 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-31 19:10:58.907  WARN 13064 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 9eb2d9d0-f4c3-4286-bc1a-6cedecc80f73

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 19:10:59.011  INFO 13064 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@170c6c17, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2b3364fc, org.springframework.security.web.context.SecurityContextPersistenceFilter@15d43c1e, org.springframework.security.web.header.HeaderWriterFilter@5a77aa91, org.springframework.web.filter.CorsFilter@3e951a28, org.springframework.security.web.authentication.logout.LogoutFilter@329712ae, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@47774550, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@fbaaa8c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1e39f03b, org.springframework.security.web.session.SessionManagementFilter@411da7a7, org.springframework.security.web.access.ExceptionTranslationFilter@13d0c842, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@22c5a25f]
2025-07-31 19:10:59.376  INFO 13064 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-31 19:10:59.427  INFO 13064 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 19:10:59.437  INFO 13064 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.532 seconds (JVM running for 6.521)
2025-07-31 19:11:01.662  INFO 13064 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 19:11:01.662  INFO 13064 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-31 19:11:01.664  INFO 13064 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-31 19:22:14.235  INFO 13064 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 19:22:14.241  INFO 13064 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-31 19:22:14.252  INFO 13064 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-31 19:22:22.772  INFO 3544 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 3544 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-07-31 19:22:22.774 DEBUG 3544 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-07-31 19:22:22.776  INFO 3544 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-07-31 19:22:22.882  INFO 3544 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-31 19:22:22.883  INFO 3544 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-31 19:22:23.964  INFO 3544 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 19:22:24.096  INFO 3544 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 118 ms. Found 10 JPA repository interfaces.
2025-07-31 19:22:25.092  INFO 3544 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-07-31 19:22:25.108  INFO 3544 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-31 19:22:25.108  INFO 3544 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-07-31 19:22:25.221  INFO 3544 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-31 19:22:25.221  INFO 3544 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2338 ms
2025-07-31 19:22:25.520  INFO 3544 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 19:22:25.620  INFO 3544 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-31 19:22:25.875  INFO 3544 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 19:22:26.036  INFO 3544 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-31 19:22:26.805  INFO 3544 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-31 19:22:26.824  INFO 3544 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-31 19:22:27.619  INFO 3544 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 19:22:27.633  INFO 3544 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 19:22:27.695  WARN 3544 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-31 19:22:28.298  WARN 3544 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: b5cfb6b4-9be6-4166-9eee-8ad55ee17db9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 19:22:28.413  INFO 3544 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6fbcc1eb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@e7cb7a1, org.springframework.security.web.context.SecurityContextPersistenceFilter@11e7606e, org.springframework.security.web.header.HeaderWriterFilter@37add1c1, org.springframework.web.filter.CorsFilter@28f07e25, org.springframework.security.web.authentication.logout.LogoutFilter@53285d2e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7ea8ffcb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@444f25ac, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@11c36f86, org.springframework.security.web.session.SessionManagementFilter@1eae4f28, org.springframework.security.web.access.ExceptionTranslationFilter@673f39f6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@29a13ce0]
2025-07-31 19:22:28.798  INFO 3544 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-31 19:22:28.836  INFO 3544 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 19:22:28.847  INFO 3544 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.66 seconds (JVM running for 7.754)
