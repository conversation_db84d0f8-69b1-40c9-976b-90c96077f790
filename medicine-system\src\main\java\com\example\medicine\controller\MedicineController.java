package com.example.medicine.controller;

import com.example.medicine.entity.Medicine;
import com.example.medicine.entity.MedicineCategory;
import com.example.medicine.service.MedicineService;
import com.example.medicine.service.MedicineCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/medicine")
public class MedicineController {

    @Autowired
    private MedicineService medicineService;

    @Autowired
    private MedicineCategoryService categoryService;

    @GetMapping("/list")
    public List<Medicine> list() {
        return medicineService.findAll();
    }

    @GetMapping("/{id}")
    public Medicine get(@PathVariable Long id) {
        return medicineService.findById(id);
    }

    @PostMapping("/add")
    public Medicine add(@RequestBody Medicine medicine) {
        return medicineService.save(medicine);
    }

    @PutMapping("/update/{id}")
    public Medicine update(@PathVariable Long id, @RequestBody Medicine medicine) {
        medicine.setId(id);
        return medicineService.save(medicine);
    }

    @DeleteMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        medicineService.deleteById(id);
    }

    @GetMapping("/category/list")
    public List<MedicineCategory> getCategoryList() {
        return categoryService.findAll();
    }
}