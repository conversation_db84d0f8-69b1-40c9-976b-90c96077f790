package com.example.medicine.controller;

import com.example.medicine.entity.Medicine;
import com.example.medicine.service.MedicineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

@RestController
@RequestMapping("/api/medicine")
public class MedicineController {

    @Autowired
    private MedicineService medicineService;

    @GetMapping("/list")
    public List<Medicine> list() {
        return medicineService.findAll();
    }

    @GetMapping("/{id}")
    public Medicine get(@PathVariable Long id) {
        return medicineService.findById(id);
    }

    @PostMapping("/add")
    public Medicine add(@RequestBody Medicine medicine) {
        return medicineService.save(medicine);
    }

    @PutMapping("/update/{id}")
    public Medicine update(@PathVariable Long id, @RequestBody Medicine medicine) {
        medicine.setId(id);
        return medicineService.save(medicine);
    }

    @DeleteMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        medicineService.deleteById(id);
    }

    @GetMapping("/category/list")
    public List<Map<String, Object>> getCategoryList() {
        List<Map<String, Object>> categories = new ArrayList<>();

        // 临时硬编码的药品分类数据
        String[] categoryNames = {
            "处方药", "非处方药", "中成药", "西药", "抗生素",
            "维生素", "心血管药", "消化系统药", "呼吸系统药", "神经系统药"
        };

        for (int i = 0; i < categoryNames.length; i++) {
            Map<String, Object> category = new HashMap<>();
            category.put("id", (long)(i + 1));
            category.put("name", categoryNames[i]);
            categories.add(category);
        }

        return categories;
    }
}